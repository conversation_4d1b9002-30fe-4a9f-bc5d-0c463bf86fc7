import { Pool } from 'pg';
import logger from './logger';
import { writePool, readPool, getWritePoolHealth, getReadPoolHealth } from '../config/database-pools';

// Connection monitoring metrics
interface ConnectionMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingClients: number;
  lastConnectionTime: Date | null;
  lastConnectionError: Error | null;
  consecutiveFailures: number;
  averageConnectionTime: number;
  connectionTimeHistory: number[];
  timeoutCount: number;
  successCount: number;
}

// Regional connection statistics
interface RegionalStats {
  region: string;
  instanceId: string;
  isHighLatencyRegion: boolean;
  metrics: ConnectionMetrics;
  lastHealthCheck: Date;
  healthCheckInterval: NodeJS.Timeout | null;
  slowConnectionCount?: number; // Optional property for tracking slow connections
}

class DatabaseConnectionMonitor {
  private stats: RegionalStats;
  private readonly maxHistorySize = 100;
  private readonly healthCheckIntervalMs = 30000; // 30 seconds

  constructor() {
    const instanceId = process.env.INSTANCE_ID || 'unknown';
    const region = process.env.REGION || 'unknown';
    const isHighLatencyRegion = this.detectHighLatencyRegion(region, instanceId);

    this.stats = {
      region,
      instanceId,
      isHighLatencyRegion,
      metrics: {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        waitingClients: 0,
        lastConnectionTime: null,
        lastConnectionError: null,
        consecutiveFailures: 0,
        averageConnectionTime: 0,
        connectionTimeHistory: [],
        timeoutCount: 0,
        successCount: 0,
      },
      lastHealthCheck: new Date(),
      healthCheckInterval: null,
      slowConnectionCount: 0, // Initialize slow connection counter
    };

    this.startHealthCheckMonitoring();
    logger.info(`Database connection monitor initialized for ${region} (${instanceId}), high-latency: ${isHighLatencyRegion}`);
  }

  private detectHighLatencyRegion(region: string, instanceId: string): boolean {
    return region.toLowerCase().includes('india') || 
           region.toLowerCase().includes('in') ||
           instanceId.toLowerCase().includes('india') ||
           instanceId.toLowerCase().includes('in');
  }

  private startHealthCheckMonitoring(): void {
    this.stats.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, this.healthCheckIntervalMs);
  }

  private async performHealthCheck(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Test write pool connection
      const writeClient = await writePool.connect();
      await writeClient.query('SELECT 1');
      writeClient.release();
      
      const connectionTime = Date.now() - startTime;
      this.recordConnectionSuccess(connectionTime);
      
      // Update pool statistics
      this.updatePoolStats();
      
      this.stats.lastHealthCheck = new Date();
      
      // Log if connection is slow (adjusted thresholds for different regions)
      const slowThreshold = this.stats.isHighLatencyRegion ? 3000 :
                           (this.stats.region.toLowerCase().includes('hk') ||
                            this.stats.region.toLowerCase().includes('hong') ? 2000 : 1000);

      if (connectionTime > slowThreshold) {
        // Only log every 10th slow connection to reduce log noise
        this.stats.slowConnectionCount = (this.stats.slowConnectionCount || 0) + 1;
        if (this.stats.slowConnectionCount % 10 === 0) {
          logger.warn(`Slow database connection detected: ${connectionTime}ms (region: ${this.stats.region}) - ${this.stats.slowConnectionCount} slow connections total`);
        }
      }
      
    } catch (error) {
      this.recordConnectionFailure(error as Error);
      logger.error(`Database health check failed for ${this.stats.region}:`, error as Error);
    }
  }

  private recordConnectionSuccess(connectionTime: number): void {
    this.stats.metrics.lastConnectionTime = new Date();
    this.stats.metrics.consecutiveFailures = 0;
    this.stats.metrics.successCount++;
    
    // Update connection time history
    this.stats.metrics.connectionTimeHistory.push(connectionTime);
    if (this.stats.metrics.connectionTimeHistory.length > this.maxHistorySize) {
      this.stats.metrics.connectionTimeHistory.shift();
    }
    
    // Calculate average connection time
    const history = this.stats.metrics.connectionTimeHistory;
    this.stats.metrics.averageConnectionTime = history.reduce((sum, time) => sum + time, 0) / history.length;
  }

  private recordConnectionFailure(error: Error): void {
    this.stats.metrics.lastConnectionError = error;
    this.stats.metrics.consecutiveFailures++;
    
    if (error.message.includes('timeout')) {
      this.stats.metrics.timeoutCount++;
    }
  }

  private updatePoolStats(): void {
    try {
      // Get write pool stats
      const writePoolStats = (writePool as any);
      this.stats.metrics.totalConnections = writePoolStats.totalCount || 0;
      this.stats.metrics.activeConnections = writePoolStats.totalCount - writePoolStats.idleCount || 0;
      this.stats.metrics.idleConnections = writePoolStats.idleCount || 0;
      this.stats.metrics.waitingClients = writePoolStats.waitingCount || 0;
    } catch (error) {
      // Pool stats might not be available in all versions
      logger.debug(`Could not retrieve pool statistics: ${(error as Error).message}`);
    }
  }

  public getConnectionStats(): RegionalStats {
    return { ...this.stats };
  }

  public getHealthSummary(): any {
    const stats = this.stats.metrics;
    const recentFailures = stats.consecutiveFailures;
    const successRate = stats.successCount > 0 ? 
      ((stats.successCount / (stats.successCount + stats.timeoutCount)) * 100).toFixed(2) : '0';
    
    return {
      region: this.stats.region,
      instanceId: this.stats.instanceId,
      isHighLatencyRegion: this.stats.isHighLatencyRegion,
      status: recentFailures < 3 ? 'healthy' : 'degraded',
      connectionHealth: {
        successRate: `${successRate}%`,
        averageConnectionTime: `${Math.round(stats.averageConnectionTime)}ms`,
        consecutiveFailures: recentFailures,
        timeoutCount: stats.timeoutCount,
        lastConnectionTime: stats.lastConnectionTime,
        lastError: stats.lastConnectionError?.message || null,
      },
      poolHealth: {
        totalConnections: stats.totalConnections,
        activeConnections: stats.activeConnections,
        idleConnections: stats.idleConnections,
        waitingClients: stats.waitingClients,
      },
      lastHealthCheck: this.stats.lastHealthCheck,
    };
  }

  public async testConnection(timeoutMs?: number): Promise<{ success: boolean; time: number; error?: string }> {
    const timeout = timeoutMs || (this.stats.isHighLatencyRegion ? 5000 : 2000);
    const startTime = Date.now();
    
    try {
      const connectionPromise = (async () => {
        const client = await writePool.connect();
        await client.query('SELECT 1 as test');
        client.release();
        return true;
      })();
      
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Connection test timeout after ${timeout}ms`)), timeout);
      });
      
      await Promise.race([connectionPromise, timeoutPromise]);
      const connectionTime = Date.now() - startTime;
      
      this.recordConnectionSuccess(connectionTime);
      return { success: true, time: connectionTime };
      
    } catch (error) {
      const connectionTime = Date.now() - startTime;
      const err = error as Error;
      this.recordConnectionFailure(err);
      
      return { 
        success: false, 
        time: connectionTime, 
        error: err.message 
      };
    }
  }

  public stop(): void {
    if (this.stats.healthCheckInterval) {
      clearInterval(this.stats.healthCheckInterval);
      this.stats.healthCheckInterval = null;
    }
  }
}

// Create singleton instance
export const dbConnectionMonitor = new DatabaseConnectionMonitor();

// Export for use in health checks and monitoring endpoints
export default dbConnectionMonitor;
